import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { ArbitrageOpportunity, ArbitrageType } from './OpportunityDetectionService.js';

export enum FlashLoanProvider {
  AAVE_V3 = 'aave_v3',
  BALANCER_V2 = 'balancer_v2',
  DYDX = 'dydx',
  UNISWAP_V3 = 'uniswap_v3',
  COMPOUND = 'compound'
}

export enum FlashLoanStrategy {
  FLASH_LOAN = 'flash_loan',
  FLASH_SWAP = 'flash_swap',
  HYBRID = 'hybrid'
}

export interface FlashLoanProviderInfo {
  provider: FlashLoanProvider;
  name: string;
  feeRate: number; // in basis points (e.g., 9 = 0.09%)
  maxLiquidity: number; // in USD
  gasOverhead: number; // additional gas cost
  supportedNetworks: string[];
  supportedAssets: string[];
  isActive: boolean;
  contractAddress: string;
  minAmount: number; // minimum flash loan amount
  maxAmount: number; // maximum flash loan amount
  healthStatus: ProviderHealthStatus;
  priority: number; // 1-10, higher is better
  lastHealthCheck: number;
  consecutiveFailures: number;
  totalRequests: number;
  successfulRequests: number;
}

export interface ProviderHealthStatus {
  isHealthy: boolean;
  latency: number;
  errorRate: number;
  lastError?: string;
  lastErrorTime?: number;
  availabilityScore: number; // 0-100
  liquidityStatus: 'high' | 'medium' | 'low' | 'unknown';
}

export interface FallbackChain {
  primary: FlashLoanProvider;
  fallbacks: FlashLoanProvider[];
  lastUsed: FlashLoanProvider;
  failoverCount: number;
  recoveryAttempts: number;
}

export interface FlashLoanQuote {
  provider: FlashLoanProvider;
  asset: string;
  amount: number;
  fee: number;
  feeRate: number;
  gasEstimate: number;
  totalCost: number;
  availableLiquidity: number;
  executionTime: number;
  isAvailable: boolean;
  reason?: string;
}

export interface FlashLoanExecution {
  provider: FlashLoanProvider;
  strategy: FlashLoanStrategy;
  asset: string;
  amount: number;
  fee: number;
  gasUsed: number;
  executionTime: number;
  success: boolean;
  transactionHash?: string;
  error?: string;
  profitAfterFees: number;
}

export interface FlashLoanOptimization {
  recommendedProvider: FlashLoanProvider;
  recommendedStrategy: FlashLoanStrategy;
  optimalAmount: number;
  totalCost: number;
  expectedProfit: number;
  riskScore: number;
  reasoning: string;
  alternatives: FlashLoanQuote[];
}

export class FlashLoanService extends EventEmitter {
  private providers: Map<string, ethers.JsonRpcProvider> = new Map();
  private flashLoanProviders: Map<FlashLoanProvider, FlashLoanProviderInfo> = new Map();
  private isRunning = false;

  // Flash loan parameters
  private readonly maxProviderCheckTime = 3000; // 3 seconds
  private readonly minProfitMargin = 0.1; // 10% minimum profit margin
  private readonly maxGasPrice = 100; // 100 Gwei max

  // Enhanced fallback mechanism
  private fallbackChains: Map<string, FallbackChain> = new Map(); // network -> fallback chain
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private readonly healthCheckIntervalMs = 30000; // 30 seconds
  private readonly maxConsecutiveFailures = 3;
  private readonly providerRecoveryTimeMs = 300000; // 5 minutes
  private readonly fallbackCooldownMs = 60000; // 1 minute
  
  constructor() {
    super();
    this.initializeProviders();
    this.initializeFlashLoanProviders();
  }

  private initializeProviders() {
    this.providers.set('ethereum', new ethers.JsonRpcProvider(config.ETHEREUM_RPC_URL));
    this.providers.set('polygon', new ethers.JsonRpcProvider(config.POLYGON_RPC_URL));
    this.providers.set('bsc', new ethers.JsonRpcProvider(config.BSC_RPC_URL));
  }

  private initializeFlashLoanProviders() {
    // Aave V3 - Primary provider with competitive rates
    this.flashLoanProviders.set(FlashLoanProvider.AAVE_V3, {
      provider: FlashLoanProvider.AAVE_V3,
      name: 'Aave V3',
      feeRate: 5, // 0.05%
      maxLiquidity: 100000000, // $100M
      gasOverhead: 150000,
      supportedNetworks: ['ethereum', 'polygon', 'arbitrum', 'optimism', 'avalanche'],
      supportedAssets: ['USDC', 'USDT', 'DAI', 'WETH', 'WBTC'],
      isActive: true,
      contractAddress: '******************************************', // Ethereum mainnet
      minAmount: 1, // $1
      maxAmount: 50000000, // $50M
      priority: 9, // High priority
      healthStatus: {
        isHealthy: true,
        latency: 0,
        errorRate: 0,
        availabilityScore: 100,
        liquidityStatus: 'high'
      },
      lastHealthCheck: 0,
      consecutiveFailures: 0,
      totalRequests: 0,
      successfulRequests: 0
    });

    // Balancer V2 - Alternative with vault-based flash loans
    this.flashLoanProviders.set(FlashLoanProvider.BALANCER_V2, {
      provider: FlashLoanProvider.BALANCER_V2,
      name: 'Balancer V2',
      feeRate: 0, // 0% fee but requires gas
      maxLiquidity: 50000000, // $50M
      gasOverhead: 120000,
      supportedNetworks: ['ethereum', 'polygon', 'arbitrum'],
      supportedAssets: ['USDC', 'USDT', 'DAI', 'WETH', 'WBTC', 'BAL'],
      isActive: true,
      contractAddress: '******************************************', // Vault address
      minAmount: 1,
      maxAmount: 25000000,
      priority: 8, // High priority (zero fees)
      healthStatus: {
        isHealthy: true,
        latency: 0,
        errorRate: 0,
        availabilityScore: 100,
        liquidityStatus: 'high'
      },
      lastHealthCheck: 0,
      consecutiveFailures: 0,
      totalRequests: 0,
      successfulRequests: 0
    });

    // dYdX - Zero-fee flash loans (where available)
    this.flashLoanProviders.set(FlashLoanProvider.DYDX, {
      provider: FlashLoanProvider.DYDX,
      name: 'dYdX',
      feeRate: 0, // 0% fee
      maxLiquidity: 20000000, // $20M
      gasOverhead: 100000,
      supportedNetworks: ['ethereum'],
      supportedAssets: ['USDC', 'WETH', 'DAI'],
      isActive: true,
      contractAddress: '******************************************', // Solo Margin
      minAmount: 1,
      maxAmount: 10000000,
      priority: 10, // Highest priority (zero fees)
      healthStatus: {
        isHealthy: true,
        latency: 0,
        errorRate: 0,
        availabilityScore: 100,
        liquidityStatus: 'medium'
      },
      lastHealthCheck: 0,
      consecutiveFailures: 0,
      totalRequests: 0,
      successfulRequests: 0
    });

    // Uniswap V3 - Flash swaps for token pairs
    this.flashLoanProviders.set(FlashLoanProvider.UNISWAP_V3, {
      provider: FlashLoanProvider.UNISWAP_V3,
      name: 'Uniswap V3',
      feeRate: 30, // 0.3% (pool fee)
      maxLiquidity: 200000000, // $200M
      gasOverhead: 80000,
      supportedNetworks: ['ethereum', 'polygon', 'arbitrum', 'optimism', 'base'],
      supportedAssets: ['USDC', 'USDT', 'DAI', 'WETH', 'WBTC', 'UNI'],
      isActive: true,
      contractAddress: '******************************************', // SwapRouter
      minAmount: 1,
      maxAmount: 100000000,
      priority: 7, // Medium-high priority
      healthStatus: {
        isHealthy: true,
        latency: 0,
        errorRate: 0,
        availabilityScore: 100,
        liquidityStatus: 'high'
      },
      lastHealthCheck: 0,
      consecutiveFailures: 0,
      totalRequests: 0,
      successfulRequests: 0
    });

    // Initialize fallback chains for each network
    this.initializeFallbackChains();
  }

  private initializeFallbackChains(): void {
    // Ethereum fallback chain
    this.fallbackChains.set('ethereum', {
      primary: FlashLoanProvider.DYDX, // Zero fees
      fallbacks: [FlashLoanProvider.BALANCER_V2, FlashLoanProvider.AAVE_V3, FlashLoanProvider.UNISWAP_V3],
      lastUsed: FlashLoanProvider.DYDX,
      failoverCount: 0,
      recoveryAttempts: 0
    });

    // Polygon fallback chain
    this.fallbackChains.set('polygon', {
      primary: FlashLoanProvider.BALANCER_V2,
      fallbacks: [FlashLoanProvider.AAVE_V3, FlashLoanProvider.UNISWAP_V3],
      lastUsed: FlashLoanProvider.BALANCER_V2,
      failoverCount: 0,
      recoveryAttempts: 0
    });

    // Arbitrum/Optimism fallback chain
    ['arbitrum', 'optimism'].forEach(network => {
      this.fallbackChains.set(network, {
        primary: FlashLoanProvider.BALANCER_V2,
        fallbacks: [FlashLoanProvider.AAVE_V3, FlashLoanProvider.UNISWAP_V3],
        lastUsed: FlashLoanProvider.BALANCER_V2,
        failoverCount: 0,
        recoveryAttempts: 0
      });
    });

    // Base fallback chain
    this.fallbackChains.set('base', {
      primary: FlashLoanProvider.UNISWAP_V3,
      fallbacks: [],
      lastUsed: FlashLoanProvider.UNISWAP_V3,
      failoverCount: 0,
      recoveryAttempts: 0
    });

    // Avalanche fallback chain
    this.fallbackChains.set('avalanche', {
      primary: FlashLoanProvider.AAVE_V3,
      fallbacks: [],
      lastUsed: FlashLoanProvider.AAVE_V3,
      failoverCount: 0,
      recoveryAttempts: 0
    });
  }
  }

  public async start() {
    if (this.isRunning) return;

    logger.info('Starting Flash Loan Service...');
    this.isRunning = true;

    // Validate provider connections
    await this.validateProviderConnections();

    // Start health monitoring
    this.startHealthMonitoring();

    logger.info('Flash Loan Service started with enhanced fallback monitoring');
  }

  public async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping Flash Loan Service...');
    this.isRunning = false;

    // Stop health monitoring
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    logger.info('Flash Loan Service stopped');
  }

  /**
   * Automatically determine optimal flash loan strategy for an arbitrage opportunity
   */
  public async optimizeFlashLoanStrategy(
    opportunity: ArbitrageOpportunity,
    availableCapital: number = 0
  ): Promise<FlashLoanOptimization> {
    
    try {
      logger.debug(`Optimizing flash loan strategy for opportunity ${opportunity.id}`);

      // Determine if flash loan is needed
      const requiredCapital = this.calculateRequiredCapital(opportunity);
      const needsFlashLoan = requiredCapital > availableCapital;

      if (!needsFlashLoan) {
        return {
          recommendedProvider: FlashLoanProvider.AAVE_V3, // Default
          recommendedStrategy: FlashLoanStrategy.FLASH_LOAN,
          optimalAmount: 0,
          totalCost: 0,
          expectedProfit: opportunity.potentialProfit,
          riskScore: 10, // Low risk without flash loan
          reasoning: 'Sufficient capital available, no flash loan needed',
          alternatives: []
        };
      }

      // Get quotes from all available providers
      const quotes = await this.getFlashLoanQuotes(
        opportunity.assets[0], // Primary asset
        requiredCapital,
        opportunity.network
      );

      // Filter available quotes
      const availableQuotes = quotes.filter(quote => quote.isAvailable);

      if (availableQuotes.length === 0) {
        throw new Error('No flash loan providers available for this opportunity');
      }

      // Determine optimal strategy
      const strategy = this.determineOptimalStrategy(opportunity, availableQuotes);
      
      // Select best provider based on total cost and risk
      const bestQuote = this.selectOptimalProvider(availableQuotes, opportunity);

      const optimization: FlashLoanOptimization = {
        recommendedProvider: bestQuote.provider,
        recommendedStrategy: strategy,
        optimalAmount: bestQuote.amount,
        totalCost: bestQuote.totalCost,
        expectedProfit: opportunity.potentialProfit - bestQuote.totalCost,
        riskScore: this.calculateFlashLoanRiskScore(bestQuote, opportunity),
        reasoning: this.generateOptimizationReasoning(bestQuote, strategy, availableQuotes),
        alternatives: availableQuotes.filter(q => q.provider !== bestQuote.provider).slice(0, 3)
      };

      logger.info(`Flash loan optimization completed for ${opportunity.id}: ${optimization.recommendedProvider} (${optimization.recommendedStrategy})`);
      
      return optimization;

    } catch (error) {
      logger.error(`Flash loan optimization failed for opportunity ${opportunity.id}:`, error);
      throw error;
    }
  }

  /**
   * Get flash loan quotes from multiple providers
   */
  public async getFlashLoanQuotes(
    asset: string,
    amount: number,
    network: string
  ): Promise<FlashLoanQuote[]> {
    
    const quotes: FlashLoanQuote[] = [];
    const startTime = Date.now();

    // Get quotes from all providers in parallel
    const quotePromises = Array.from(this.flashLoanProviders.values())
      .filter(provider => 
        provider.isActive && 
        provider.supportedNetworks.includes(network) &&
        provider.supportedAssets.includes(asset)
      )
      .map(provider => this.getProviderQuote(provider, asset, amount, network));

    try {
      const results = await Promise.allSettled(quotePromises);
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          quotes.push(result.value);
        } else {
          logger.warn(`Flash loan quote failed for provider:`, result.reason);
        }
      });

      const executionTime = Date.now() - startTime;
      logger.debug(`Retrieved ${quotes.length} flash loan quotes in ${executionTime}ms`);

      return quotes.sort((a, b) => a.totalCost - b.totalCost); // Sort by total cost

    } catch (error) {
      logger.error('Error getting flash loan quotes:', error);
      return [];
    }
  }

  /**
   * Get quote from a specific provider
   */
  private async getProviderQuote(
    provider: FlashLoanProviderInfo,
    asset: string,
    amount: number,
    network: string
  ): Promise<FlashLoanQuote> {
    
    const startTime = Date.now();

    try {
      // Check amount limits
      if (amount < provider.minAmount || amount > provider.maxAmount) {
        return {
          provider: provider.provider,
          asset,
          amount,
          fee: 0,
          feeRate: provider.feeRate,
          gasEstimate: 0,
          totalCost: Infinity,
          availableLiquidity: 0,
          executionTime: Date.now() - startTime,
          isAvailable: false,
          reason: `Amount ${amount} outside limits [${provider.minAmount}, ${provider.maxAmount}]`
        };
      }

      // Get available liquidity
      const availableLiquidity = await this.getProviderLiquidity(provider, asset, network);
      
      if (availableLiquidity < amount) {
        return {
          provider: provider.provider,
          asset,
          amount,
          fee: 0,
          feeRate: provider.feeRate,
          gasEstimate: 0,
          totalCost: Infinity,
          availableLiquidity,
          executionTime: Date.now() - startTime,
          isAvailable: false,
          reason: `Insufficient liquidity: ${availableLiquidity} < ${amount}`
        };
      }

      // Calculate fees and costs
      const fee = (amount * provider.feeRate) / 10000; // Convert basis points to decimal
      const gasEstimate = provider.gasOverhead;
      const gasCost = await this.estimateGasCost(gasEstimate, network);
      const totalCost = fee + gasCost;

      return {
        provider: provider.provider,
        asset,
        amount,
        fee,
        feeRate: provider.feeRate,
        gasEstimate,
        totalCost,
        availableLiquidity,
        executionTime: Date.now() - startTime,
        isAvailable: true
      };

    } catch (error) {
      logger.error(`Error getting quote from ${provider.name}:`, error);
      
      return {
        provider: provider.provider,
        asset,
        amount,
        fee: 0,
        feeRate: provider.feeRate,
        gasEstimate: 0,
        totalCost: Infinity,
        availableLiquidity: 0,
        executionTime: Date.now() - startTime,
        isAvailable: false,
        reason: `Provider error: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * Calculate required capital for arbitrage opportunity
   */
  private calculateRequiredCapital(opportunity: ArbitrageOpportunity): number {
    // For intra-chain arbitrage, need capital for initial purchase
    if (opportunity.type === ArbitrageType.INTRA_CHAIN) {
      return opportunity.potentialProfit * 10; // Rough estimate: 10x profit as capital
    }

    // For cross-chain arbitrage, need capital for both sides
    if (opportunity.type === ArbitrageType.CROSS_CHAIN) {
      return opportunity.potentialProfit * 15; // Higher capital for cross-chain
    }

    // For triangular arbitrage, need capital for initial swap
    if (opportunity.type === ArbitrageType.TRIANGULAR) {
      return opportunity.potentialProfit * 12; // Medium capital requirement
    }

    return opportunity.potentialProfit * 10; // Default
  }

  /**
   * Determine optimal strategy based on opportunity characteristics
   */
  private determineOptimalStrategy(
    opportunity: ArbitrageOpportunity,
    quotes: FlashLoanQuote[]
  ): FlashLoanStrategy {

    // For token-to-token arbitrage with Uniswap, prefer flash swaps
    if (opportunity.type === ArbitrageType.INTRA_CHAIN &&
        opportunity.exchanges.some(ex => ex.toLowerCase().includes('uniswap'))) {
      return FlashLoanStrategy.FLASH_SWAP;
    }

    // For cross-chain arbitrage, use traditional flash loans
    if (opportunity.type === ArbitrageType.CROSS_CHAIN) {
      return FlashLoanStrategy.FLASH_LOAN;
    }

    // For triangular arbitrage, consider hybrid approach
    if (opportunity.type === ArbitrageType.TRIANGULAR) {
      return FlashLoanStrategy.HYBRID;
    }

    // Default to flash loan
    return FlashLoanStrategy.FLASH_LOAN;
  }

  /**
   * Select optimal provider based on cost and risk
   */
  private selectOptimalProvider(
    quotes: FlashLoanQuote[],
    opportunity: ArbitrageOpportunity
  ): FlashLoanQuote {

    if (quotes.length === 0) {
      throw new Error('No available flash loan quotes');
    }

    // Score each quote based on multiple factors
    const scoredQuotes = quotes.map(quote => {
      let score = 0;

      // Cost factor (lower cost = higher score)
      const costScore = Math.max(0, 100 - (quote.totalCost / opportunity.potentialProfit) * 100);
      score += costScore * 0.4;

      // Liquidity factor (higher liquidity = higher score)
      const liquidityScore = Math.min(100, (quote.availableLiquidity / quote.amount) * 10);
      score += liquidityScore * 0.3;

      // Speed factor (lower execution time = higher score)
      const speedScore = Math.max(0, 100 - quote.executionTime / 100);
      score += speedScore * 0.2;

      // Provider reliability factor
      const reliabilityScore = this.getProviderReliabilityScore(quote.provider);
      score += reliabilityScore * 0.1;

      return { quote, score };
    });

    // Sort by score and return best quote
    scoredQuotes.sort((a, b) => b.score - a.score);
    return scoredQuotes[0].quote;
  }

  /**
   * Calculate flash loan risk score
   */
  private calculateFlashLoanRiskScore(
    quote: FlashLoanQuote,
    opportunity: ArbitrageOpportunity
  ): number {
    let riskScore = 0;

    // Cost ratio risk (higher cost ratio = higher risk)
    const costRatio = quote.totalCost / opportunity.potentialProfit;
    riskScore += Math.min(costRatio * 50, 30);

    // Liquidity risk (lower liquidity buffer = higher risk)
    const liquidityBuffer = quote.availableLiquidity / quote.amount;
    if (liquidityBuffer < 2) riskScore += 20;
    else if (liquidityBuffer < 5) riskScore += 10;

    // Provider risk
    const providerRisk = this.getProviderRiskScore(quote.provider);
    riskScore += providerRisk;

    // Network congestion risk
    if (opportunity.network === 'ethereum') riskScore += 10;

    return Math.min(Math.round(riskScore), 100);
  }

  /**
   * Generate optimization reasoning
   */
  private generateOptimizationReasoning(
    bestQuote: FlashLoanQuote,
    strategy: FlashLoanStrategy,
    alternatives: FlashLoanQuote[]
  ): string {
    const provider = this.flashLoanProviders.get(bestQuote.provider);
    const savings = alternatives.length > 0 ?
      Math.min(...alternatives.map(q => q.totalCost)) - bestQuote.totalCost : 0;

    let reasoning = `Selected ${provider?.name} for ${strategy} strategy. `;
    reasoning += `Total cost: $${bestQuote.totalCost.toFixed(2)} `;
    reasoning += `(${bestQuote.feeRate / 100}% fee + gas). `;

    if (savings > 0) {
      reasoning += `Saves $${savings.toFixed(2)} vs alternatives. `;
    }

    reasoning += `Liquidity: $${bestQuote.availableLiquidity.toLocaleString()}.`;

    return reasoning;
  }

  /**
   * Get provider liquidity for specific asset
   */
  private async getProviderLiquidity(
    provider: FlashLoanProviderInfo,
    asset: string,
    network: string
  ): Promise<number> {

    try {
      // In production, would query actual contract liquidity
      // For demo, return simulated liquidity based on provider limits

      const baseMultiplier = Math.random() * 0.5 + 0.5; // 50-100% of max
      return provider.maxLiquidity * baseMultiplier;

    } catch (error) {
      logger.error(`Error getting liquidity for ${provider.name}:`, error);
      return 0;
    }
  }

  /**
   * Estimate gas cost for transaction
   */
  private async estimateGasCost(gasUnits: number, network: string): Promise<number> {
    try {
      const provider = this.providers.get(network);
      if (!provider) return gasUnits * 20 * 1e-9 * 2000; // Default estimate

      const feeData = await provider.getFeeData();
      const gasPrice = Number(feeData.gasPrice || 0) / 1e9; // Convert to Gwei

      // Convert to USD (simplified)
      const nativeToUsd = network === 'ethereum' ? 2000 :
                         network === 'polygon' ? 0.8 : 300;

      return (gasUnits * gasPrice * nativeToUsd) / 1e9;

    } catch (error) {
      logger.error(`Error estimating gas cost for ${network}:`, error);
      return gasUnits * 20 * 1e-9 * 2000; // Fallback estimate
    }
  }

  /**
   * Get provider reliability score
   */
  private getProviderReliabilityScore(provider: FlashLoanProvider): number {
    const scores: { [key in FlashLoanProvider]: number } = {
      [FlashLoanProvider.AAVE_V3]: 95,
      [FlashLoanProvider.BALANCER_V2]: 90,
      [FlashLoanProvider.DYDX]: 85,
      [FlashLoanProvider.UNISWAP_V3]: 88,
      [FlashLoanProvider.COMPOUND]: 82
    };

    return scores[provider] || 70;
  }

  /**
   * Get provider risk score
   */
  private getProviderRiskScore(provider: FlashLoanProvider): number {
    const riskScores: { [key in FlashLoanProvider]: number } = {
      [FlashLoanProvider.AAVE_V3]: 5,
      [FlashLoanProvider.BALANCER_V2]: 8,
      [FlashLoanProvider.DYDX]: 10,
      [FlashLoanProvider.UNISWAP_V3]: 7,
      [FlashLoanProvider.COMPOUND]: 12
    };

    return riskScores[provider] || 15;
  }

  /**
   * Validate provider connections
   */
  private async validateProviderConnections(): Promise<void> {
    logger.info('Validating flash loan provider connections...');

    let validProviders = 0;
    const totalProviders = this.flashLoanProviders.size;

    for (const [providerType, provider] of this.flashLoanProviders) {
      try {
        // In production, would check actual contract availability
        // For demo, simulate validation
        await new Promise(resolve => setTimeout(resolve, 100));

        logger.debug(`✅ ${provider.name} connection validated`);
        validProviders++;

      } catch (error) {
        logger.warn(`❌ ${provider.name} connection failed:`, error);
        provider.isActive = false;
      }
    }

    logger.info(`Flash loan providers validated: ${validProviders}/${totalProviders} active`);
  }

  /**
   * Get flash loan service statistics
   */
  public getFlashLoanStats() {
    const activeProviders = Array.from(this.flashLoanProviders.values())
      .filter(p => p.isActive);

    return {
      isRunning: this.isRunning,
      totalProviders: this.flashLoanProviders.size,
      activeProviders: activeProviders.length,
      supportedNetworks: [...new Set(activeProviders.flatMap(p => p.supportedNetworks))],
      supportedAssets: [...new Set(activeProviders.flatMap(p => p.supportedAssets))],
      providers: activeProviders.map(p => ({
        name: p.name,
        feeRate: p.feeRate,
        maxLiquidity: p.maxLiquidity,
        networks: p.supportedNetworks
      }))
    };
  }

  /**
   * Get provider information
   */
  public getProviderInfo(provider: FlashLoanProvider): FlashLoanProviderInfo | undefined {
    return this.flashLoanProviders.get(provider);
  }

  /**
   * Check if flash loan is available for opportunity
   */
  public async isFlashLoanAvailable(
    opportunity: ArbitrageOpportunity,
    requiredAmount: number
  ): Promise<boolean> {

    try {
      const quotes = await this.getFlashLoanQuotes(
        opportunity.assets[0],
        requiredAmount,
        opportunity.network
      );

      return quotes.some(quote => quote.isAvailable);

    } catch (error) {
      logger.error('Error checking flash loan availability:', error);
      return false;
    }
  }

  /**
   * Start health monitoring for all providers
   */
  private startHealthMonitoring(): void {
    if (this.healthCheckInterval) return;

    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthChecks();
    }, this.healthCheckIntervalMs);

    logger.info('Flash loan provider health monitoring started');
  }

  /**
   * Perform health checks on all providers
   */
  private async performHealthChecks(): Promise<void> {
    const healthCheckPromises = Array.from(this.flashLoanProviders.values())
      .map(provider => this.checkProviderHealth(provider));

    try {
      await Promise.allSettled(healthCheckPromises);

      // Update fallback chains based on health status
      this.updateFallbackChains();

      // Emit health status update
      this.emit('healthStatusUpdated', this.getProviderHealthSummary());

    } catch (error) {
      logger.error('Error during provider health checks:', error);
    }
  }

  /**
   * Check health of a specific provider
   */
  private async checkProviderHealth(provider: FlashLoanProviderInfo): Promise<void> {
    const startTime = Date.now();

    try {
      // Simulate health check (in production, would check actual contract availability)
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));

      const latency = Date.now() - startTime;

      // Update health status
      provider.healthStatus.isHealthy = true;
      provider.healthStatus.latency = latency;
      provider.healthStatus.availabilityScore = Math.min(100, provider.healthStatus.availabilityScore + 1);
      provider.lastHealthCheck = Date.now();
      provider.consecutiveFailures = 0;

      // Calculate error rate
      if (provider.totalRequests > 0) {
        provider.healthStatus.errorRate = ((provider.totalRequests - provider.successfulRequests) / provider.totalRequests) * 100;
      }

    } catch (error) {
      // Update failure status
      provider.healthStatus.isHealthy = false;
      provider.healthStatus.lastError = error instanceof Error ? error.message : 'Unknown error';
      provider.healthStatus.lastErrorTime = Date.now();
      provider.healthStatus.availabilityScore = Math.max(0, provider.healthStatus.availabilityScore - 10);
      provider.consecutiveFailures++;

      // Deactivate provider if too many consecutive failures
      if (provider.consecutiveFailures >= this.maxConsecutiveFailures) {
        provider.isActive = false;
        logger.warn(`Provider ${provider.name} deactivated due to consecutive failures`);

        // Emit provider failure event
        this.emit('providerFailed', {
          provider: provider.provider,
          name: provider.name,
          consecutiveFailures: provider.consecutiveFailures,
          lastError: provider.healthStatus.lastError
        });
      }
    }
  }

  /**
   * Update fallback chains based on current provider health
   */
  private updateFallbackChains(): void {
    for (const [network, chain] of this.fallbackChains) {
      const primaryProvider = this.flashLoanProviders.get(chain.primary);

      if (primaryProvider && !primaryProvider.isActive) {
        // Primary provider is down, find best fallback
        const bestFallback = this.findBestFallbackProvider(network, chain.fallbacks);

        if (bestFallback && bestFallback !== chain.lastUsed) {
          chain.lastUsed = bestFallback;
          chain.failoverCount++;

          logger.info(`Failover activated for ${network}: ${chain.primary} -> ${bestFallback}`);

          this.emit('failoverActivated', {
            network,
            from: chain.primary,
            to: bestFallback,
            failoverCount: chain.failoverCount
          });
        }
      } else if (primaryProvider && primaryProvider.isActive && chain.lastUsed !== chain.primary) {
        // Primary provider recovered, attempt recovery
        const timeSinceFailure = Date.now() - (primaryProvider.healthStatus.lastErrorTime || 0);

        if (timeSinceFailure > this.providerRecoveryTimeMs) {
          chain.lastUsed = chain.primary;
          chain.recoveryAttempts++;

          logger.info(`Provider recovery for ${network}: ${chain.primary} restored as primary`);

          this.emit('providerRecovered', {
            network,
            provider: chain.primary,
            recoveryAttempts: chain.recoveryAttempts
          });
        }
      }
    }
  }

  /**
   * Find best fallback provider for a network
   */
  private findBestFallbackProvider(network: string, fallbacks: FlashLoanProvider[]): FlashLoanProvider | null {
    const availableProviders = fallbacks
      .map(providerId => this.flashLoanProviders.get(providerId))
      .filter((provider): provider is FlashLoanProviderInfo =>
        provider !== undefined &&
        provider.isActive &&
        provider.supportedNetworks.includes(network)
      )
      .sort((a, b) => {
        // Sort by priority and health score
        const scoreA = a.priority * 10 + a.healthStatus.availabilityScore;
        const scoreB = b.priority * 10 + b.healthStatus.availabilityScore;
        return scoreB - scoreA;
      });

    return availableProviders.length > 0 ? availableProviders[0].provider : null;
  }

  /**
   * Get provider with fallback support
   */
  public async getProviderWithFallback(
    network: string,
    asset: string,
    amount: number
  ): Promise<FlashLoanProviderInfo | null> {

    const chain = this.fallbackChains.get(network);
    if (!chain) {
      // No fallback chain, use best available provider
      return this.getBestAvailableProvider(network, asset, amount);
    }

    // Try current provider (primary or fallback)
    const currentProvider = this.flashLoanProviders.get(chain.lastUsed);
    if (currentProvider && this.isProviderSuitable(currentProvider, network, asset, amount)) {
      return currentProvider;
    }

    // Current provider not suitable, try fallbacks
    for (const fallbackId of chain.fallbacks) {
      const fallbackProvider = this.flashLoanProviders.get(fallbackId);
      if (fallbackProvider && this.isProviderSuitable(fallbackProvider, network, asset, amount)) {
        // Update chain to use this fallback
        chain.lastUsed = fallbackId;
        return fallbackProvider;
      }
    }

    // No suitable provider found in chain
    logger.warn(`No suitable flash loan provider found for ${network}/${asset}/${amount}`);
    return null;
  }

  /**
   * Check if provider is suitable for request
   */
  private isProviderSuitable(
    provider: FlashLoanProviderInfo,
    network: string,
    asset: string,
    amount: number
  ): boolean {
    return provider.isActive &&
           provider.healthStatus.isHealthy &&
           provider.supportedNetworks.includes(network) &&
           provider.supportedAssets.includes(asset) &&
           amount >= provider.minAmount &&
           amount <= provider.maxAmount;
  }

  /**
   * Get best available provider without fallback chain
   */
  private getBestAvailableProvider(
    network: string,
    asset: string,
    amount: number
  ): FlashLoanProviderInfo | null {

    const suitableProviders = Array.from(this.flashLoanProviders.values())
      .filter(provider => this.isProviderSuitable(provider, network, asset, amount))
      .sort((a, b) => {
        // Sort by priority and health, then by fee rate
        const scoreA = a.priority * 10 + a.healthStatus.availabilityScore - a.feeRate;
        const scoreB = b.priority * 10 + b.healthStatus.availabilityScore - b.feeRate;
        return scoreB - scoreA;
      });

    return suitableProviders.length > 0 ? suitableProviders[0] : null;
  }

  /**
   * Get provider health summary
   */
  public getProviderHealthSummary(): { [key: string]: any } {
    const summary: { [key: string]: any } = {};

    for (const [providerId, provider] of this.flashLoanProviders) {
      summary[provider.name] = {
        isActive: provider.isActive,
        isHealthy: provider.healthStatus.isHealthy,
        latency: provider.healthStatus.latency,
        errorRate: provider.healthStatus.errorRate,
        availabilityScore: provider.healthStatus.availabilityScore,
        consecutiveFailures: provider.consecutiveFailures,
        totalRequests: provider.totalRequests,
        successRate: provider.totalRequests > 0 ?
          (provider.successfulRequests / provider.totalRequests * 100).toFixed(2) + '%' : 'N/A',
        lastHealthCheck: new Date(provider.lastHealthCheck).toISOString(),
        supportedNetworks: provider.supportedNetworks,
        priority: provider.priority
      };
    }

    return summary;
  }

  /**
   * Get fallback chain status
   */
  public getFallbackChainStatus(): { [key: string]: any } {
    const status: { [key: string]: any } = {};

    for (const [network, chain] of this.fallbackChains) {
      const primaryProvider = this.flashLoanProviders.get(chain.primary);
      const currentProvider = this.flashLoanProviders.get(chain.lastUsed);

      status[network] = {
        primary: primaryProvider?.name || 'Unknown',
        current: currentProvider?.name || 'Unknown',
        isUsingFallback: chain.lastUsed !== chain.primary,
        failoverCount: chain.failoverCount,
        recoveryAttempts: chain.recoveryAttempts,
        availableFallbacks: chain.fallbacks.map(id =>
          this.flashLoanProviders.get(id)?.name || 'Unknown'
        )
      };
    }

    return status;
  }

  /**
   * Force provider recovery attempt
   */
  public async forceProviderRecovery(providerId: FlashLoanProvider): Promise<boolean> {
    const provider = this.flashLoanProviders.get(providerId);
    if (!provider) {
      logger.warn(`Provider ${providerId} not found for recovery`);
      return false;
    }

    logger.info(`Forcing recovery attempt for provider: ${provider.name}`);

    try {
      // Reset failure counters
      provider.consecutiveFailures = 0;
      provider.isActive = true;
      provider.healthStatus.isHealthy = true;
      provider.healthStatus.lastError = undefined;
      provider.healthStatus.lastErrorTime = undefined;

      // Perform immediate health check
      await this.checkProviderHealth(provider);

      if (provider.healthStatus.isHealthy) {
        logger.info(`Provider ${provider.name} recovery successful`);

        // Update fallback chains
        this.updateFallbackChains();

        this.emit('providerRecoveryForced', {
          provider: providerId,
          name: provider.name,
          success: true
        });

        return true;
      } else {
        logger.warn(`Provider ${provider.name} recovery failed - still unhealthy`);
        return false;
      }

    } catch (error) {
      logger.error(`Error during forced recovery of ${provider.name}:`, error);

      this.emit('providerRecoveryForced', {
        provider: providerId,
        name: provider.name,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return false;
    }
  }

  /**
   * Get comprehensive service statistics
   */
  public getServiceStatistics(): { [key: string]: any } {
    const totalRequests = Array.from(this.flashLoanProviders.values())
      .reduce((sum, provider) => sum + provider.totalRequests, 0);

    const totalSuccessful = Array.from(this.flashLoanProviders.values())
      .reduce((sum, provider) => sum + provider.successfulRequests, 0);

    const activeProviders = Array.from(this.flashLoanProviders.values())
      .filter(provider => provider.isActive).length;

    const healthyProviders = Array.from(this.flashLoanProviders.values())
      .filter(provider => provider.healthStatus.isHealthy).length;

    return {
      isRunning: this.isRunning,
      totalProviders: this.flashLoanProviders.size,
      activeProviders,
      healthyProviders,
      totalRequests,
      totalSuccessful,
      overallSuccessRate: totalRequests > 0 ?
        (totalSuccessful / totalRequests * 100).toFixed(2) + '%' : 'N/A',
      supportedNetworks: Array.from(new Set(
        Array.from(this.flashLoanProviders.values())
          .flatMap(provider => provider.supportedNetworks)
      )),
      fallbackChainsConfigured: this.fallbackChains.size,
      healthMonitoringActive: this.healthCheckInterval !== null
    };
  }
}
